import asyncio
import logging
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union

import asyncpg
import numpy as np
from pgvector.asyncpg import register_vector
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class EmbeddingDocument(BaseModel):
    """Model for embedding documents"""

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()))
    content: str
    metadata: Dict[str, Any] = Field(default_factory=dict)
    embedding: Optional[List[float]] = None
    collection_name: str = "default"
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class CollectionInfo(BaseModel):
    """Model for collection information"""

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: Optional[str] = None
    embedding_model: str = "text-embedding-ada-002"
    embedding_dimension: int = 1536
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class SearchResult(BaseModel):
    """Model for search results"""

    document: EmbeddingDocument
    similarity_score: float
    rank: int


class PGVectorStore:
    """
    PostgreSQL vector store with pgvector extension for efficient embedding storage and retrieval.

    Features:
    - Async operations for high performance
    - Collection-based organization
    - Efficient similarity search with cosine distance
    - Batch operations for large datasets
    - Token-efficient updates (only update changed documents)
    - Comprehensive metadata support
    """

    def __init__(self, connection_string: str, pool_size: int = 10):
        """
        Initialize the PGVectorStore.

        Args:
            connection_string: PostgreSQL connection string
            pool_size: Connection pool size for async operations
        """
        self.connection_string = connection_string
        self.pool_size = pool_size
        self.pool: Optional[asyncpg.Pool] = None

    async def initialize(self) -> None:
        """Initialize the connection pool and register vector types."""
        try:
            self.pool = await asyncpg.create_pool(
                self.connection_string,
                min_size=1,
                max_size=self.pool_size,
                command_timeout=60,
            )

            # Register vector type for each connection in the pool
            async with self.pool.acquire() as conn:
                await register_vector(conn)

            logger.info("PGVectorStore initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize PGVectorStore: {e}")
            raise

    async def close(self) -> None:
        """Close the connection pool."""
        if self.pool:
            await self.pool.close()
            logger.info("PGVectorStore connection pool closed")

    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()

    async def create_collection(self, collection: CollectionInfo) -> str:
        """
        Create a new collection.

        Args:
            collection: Collection information

        Returns:
            Collection ID
        """
        async with self.pool.acquire() as conn:
            try:
                result = await conn.fetchrow(
                    """
                    INSERT INTO embeddings.collections
                    (id, name, description, embedding_model, embedding_dimension, metadata)
                    VALUES ($1, $2, $3, $4, $5, $6)
                    ON CONFLICT (name) DO UPDATE SET
                        description = EXCLUDED.description,
                        embedding_model = EXCLUDED.embedding_model,
                        embedding_dimension = EXCLUDED.embedding_dimension,
                        metadata = EXCLUDED.metadata,
                        updated_at = NOW()
                    RETURNING id
                """,
                    collection.id,
                    collection.name,
                    collection.description,
                    collection.embedding_model,
                    collection.embedding_dimension,
                    collection.metadata,
                )

                collection_id = result["id"]
                logger.info(
                    f"Collection '{collection.name}' created/updated with ID: {collection_id}"
                )
                return collection_id

            except Exception as e:
                logger.error(f"Failed to create collection '{collection.name}': {e}")
                raise

    async def get_collection(self, name: str) -> Optional[CollectionInfo]:
        """
        Get collection information by name.

        Args:
            name: Collection name

        Returns:
            Collection information or None if not found
        """
        async with self.pool.acquire() as conn:
            try:
                result = await conn.fetchrow(
                    """
                    SELECT id, name, description, embedding_model, embedding_dimension,
                           metadata, created_at, updated_at
                    FROM embeddings.collections
                    WHERE name = $1
                """,
                    name,
                )

                if result:
                    return CollectionInfo(
                        id=result["id"],
                        name=result["name"],
                        description=result["description"],
                        embedding_model=result["embedding_model"],
                        embedding_dimension=result["embedding_dimension"],
                        metadata=result["metadata"] or {},
                        created_at=result["created_at"],
                        updated_at=result["updated_at"],
                    )
                return None

            except Exception as e:
                logger.error(f"Failed to get collection '{name}': {e}")
                raise

    async def list_collections(self) -> List[CollectionInfo]:
        """
        List all collections.

        Returns:
            List of collection information
        """
        async with self.pool.acquire() as conn:
            try:
                results = await conn.fetch(
                    """
                    SELECT id, name, description, embedding_model, embedding_dimension,
                           metadata, created_at, updated_at
                    FROM embeddings.collections
                    ORDER BY created_at DESC
                """
                )

                return [
                    CollectionInfo(
                        id=row["id"],
                        name=row["name"],
                        description=row["description"],
                        embedding_model=row["embedding_model"],
                        embedding_dimension=row["embedding_dimension"],
                        metadata=row["metadata"] or {},
                        created_at=row["created_at"],
                        updated_at=row["updated_at"],
                    )
                    for row in results
                ]

            except Exception as e:
                logger.error(f"Failed to list collections: {e}")
                raise

    async def delete_collection(self, name: str) -> bool:
        """
        Delete a collection and all its documents.

        Args:
            name: Collection name

        Returns:
            True if collection was deleted, False if not found
        """
        async with self.pool.acquire() as conn:
            try:
                async with conn.transaction():
                    # Delete all documents in the collection
                    await conn.execute(
                        """
                        DELETE FROM embeddings.documents
                        WHERE collection_name = $1
                    """,
                        name,
                    )

                    # Delete the collection
                    result = await conn.execute(
                        """
                        DELETE FROM embeddings.collections
                        WHERE name = $1
                    """,
                        name,
                    )

                    deleted = result.split()[-1] == "1"
                    if deleted:
                        logger.info(
                            f"Collection '{name}' and all its documents deleted"
                        )
                    else:
                        logger.warning(f"Collection '{name}' not found")

                    return deleted

            except Exception as e:
                logger.error(f"Failed to delete collection '{name}': {e}")
                raise

    async def add_documents(
        self, documents: List[EmbeddingDocument], batch_size: int = 100
    ) -> List[str]:
        """
        Add multiple documents to the store in batches.

        Args:
            documents: List of documents to add
            batch_size: Number of documents to process in each batch

        Returns:
            List of document IDs
        """
        if not documents:
            return []

        document_ids = []

        # Process documents in batches
        for i in range(0, len(documents), batch_size):
            batch = documents[i : i + batch_size]
            batch_ids = await self._add_documents_batch(batch)
            document_ids.extend(batch_ids)

        logger.info(
            f"Added {len(documents)} documents across {len(document_ids)} batches"
        )
        return document_ids

    async def _add_documents_batch(
        self, documents: List[EmbeddingDocument]
    ) -> List[str]:
        """Add a batch of documents."""
        async with self.pool.acquire() as conn:
            try:
                async with conn.transaction():
                    document_ids = []

                    for doc in documents:
                        # Ensure document has an ID
                        if not doc.id:
                            doc.id = str(uuid.uuid4())

                        # Convert embedding to vector format
                        embedding_vector = None
                        if doc.embedding:
                            embedding_vector = np.array(doc.embedding, dtype=np.float32)

                        result = await conn.fetchrow(
                            """
                            INSERT INTO embeddings.documents
                            (id, content, metadata, embedding, collection_name)
                            VALUES ($1, $2, $3, $4, $5)
                            ON CONFLICT (id) DO UPDATE SET
                                content = EXCLUDED.content,
                                metadata = EXCLUDED.metadata,
                                embedding = EXCLUDED.embedding,
                                collection_name = EXCLUDED.collection_name,
                                updated_at = NOW()
                            RETURNING id
                        """,
                            doc.id,
                            doc.content,
                            doc.metadata,
                            embedding_vector,
                            doc.collection_name,
                        )

                        document_ids.append(result["id"])

                    return document_ids

            except Exception as e:
                logger.error(f"Failed to add document batch: {e}")
                raise

    async def get_document(self, document_id: str) -> Optional[EmbeddingDocument]:
        """
        Get a document by ID.

        Args:
            document_id: Document ID

        Returns:
            Document or None if not found
        """
        async with self.pool.acquire() as conn:
            try:
                result = await conn.fetchrow(
                    """
                    SELECT id, content, metadata, embedding, collection_name,
                           created_at, updated_at
                    FROM embeddings.documents
                    WHERE id = $1
                """,
                    document_id,
                )

                if result:
                    embedding = None
                    if result["embedding"]:
                        embedding = result["embedding"].tolist()

                    return EmbeddingDocument(
                        id=result["id"],
                        content=result["content"],
                        metadata=result["metadata"] or {},
                        embedding=embedding,
                        collection_name=result["collection_name"],
                        created_at=result["created_at"],
                        updated_at=result["updated_at"],
                    )
                return None

            except Exception as e:
                logger.error(f"Failed to get document '{document_id}': {e}")
                raise

    async def similarity_search(
        self,
        query_embedding: List[float],
        collection_name: str = "default",
        limit: int = 10,
        similarity_threshold: float = 0.0,
        metadata_filter: Optional[Dict[str, Any]] = None,
    ) -> List[SearchResult]:
        """
        Perform similarity search using cosine distance.

        Args:
            query_embedding: Query embedding vector
            collection_name: Collection to search in
            limit: Maximum number of results
            similarity_threshold: Minimum similarity score (0-1)
            metadata_filter: Optional metadata filter

        Returns:
            List of search results ordered by similarity
        """
        async with self.pool.acquire() as conn:
            try:
                query_vector = np.array(query_embedding, dtype=np.float32)

                # Build the query with optional metadata filter
                base_query = """
                    SELECT id, content, metadata, embedding, collection_name,
                           created_at, updated_at,
                           1 - (embedding <=> $1) AS similarity_score
                    FROM embeddings.documents
                    WHERE collection_name = $2
                """

                params = [query_vector, collection_name]
                param_count = 2

                # Add metadata filter if provided
                if metadata_filter:
                    conditions = []
                    for key, value in metadata_filter.items():
                        param_count += 1
                        conditions.append(f"metadata->>'{key}' = ${param_count}")
                        params.append(str(value))

                    if conditions:
                        base_query += " AND " + " AND ".join(conditions)

                # Add similarity threshold filter
                if similarity_threshold > 0:
                    param_count += 1
                    base_query += f" AND (1 - (embedding <=> $1)) >= ${param_count}"
                    params.append(similarity_threshold)

                # Order by similarity and limit
                base_query += " ORDER BY similarity_score DESC LIMIT $" + str(
                    param_count + 1
                )
                params.append(limit)

                results = await conn.fetch(base_query, *params)

                search_results = []
                for rank, row in enumerate(results, 1):
                    embedding = None
                    if row["embedding"]:
                        embedding = row["embedding"].tolist()

                    document = EmbeddingDocument(
                        id=row["id"],
                        content=row["content"],
                        metadata=row["metadata"] or {},
                        embedding=embedding,
                        collection_name=row["collection_name"],
                        created_at=row["created_at"],
                        updated_at=row["updated_at"],
                    )

                    search_results.append(
                        SearchResult(
                            document=document,
                            similarity_score=float(row["similarity_score"]),
                            rank=rank,
                        )
                    )

                logger.info(
                    f"Found {len(search_results)} similar documents in collection '{collection_name}'"
                )
                return search_results

            except Exception as e:
                logger.error(f"Failed to perform similarity search: {e}")
                raise

    async def update_embeddings(
        self,
        documents: List[EmbeddingDocument],
        batch_size: int = 100,
        only_if_changed: bool = True,
    ) -> Tuple[int, int]:
        """
        Update embeddings for existing documents efficiently.
        Only updates documents that have actually changed to save tokens.

        Args:
            documents: List of documents with updated embeddings
            batch_size: Number of documents to process in each batch
            only_if_changed: Only update if content or embedding has changed

        Returns:
            Tuple of (updated_count, skipped_count)
        """
        if not documents:
            return 0, 0

        updated_count = 0
        skipped_count = 0

        # Process documents in batches
        for i in range(0, len(documents), batch_size):
            batch = documents[i : i + batch_size]
            batch_updated, batch_skipped = await self._update_embeddings_batch(
                batch, only_if_changed
            )
            updated_count += batch_updated
            skipped_count += batch_skipped

        logger.info(
            f"Updated {updated_count} documents, skipped {skipped_count} unchanged documents"
        )
        return updated_count, skipped_count

    async def _update_embeddings_batch(
        self, documents: List[EmbeddingDocument], only_if_changed: bool
    ) -> Tuple[int, int]:
        """Update a batch of document embeddings."""
        async with self.pool.acquire() as conn:
            try:
                updated_count = 0
                skipped_count = 0

                async with conn.transaction():
                    for doc in documents:
                        if only_if_changed:
                            # Check if document has changed
                            existing = await conn.fetchrow(
                                """
                                SELECT content, embedding
                                FROM embeddings.documents
                                WHERE id = $1
                            """,
                                doc.id,
                            )

                            if existing:
                                # Compare content and embedding
                                content_changed = existing["content"] != doc.content

                                embedding_changed = False
                                if doc.embedding and existing["embedding"]:
                                    existing_embedding = existing["embedding"].tolist()
                                    embedding_changed = (
                                        existing_embedding != doc.embedding
                                    )
                                elif doc.embedding or existing["embedding"]:
                                    embedding_changed = True

                                if not (content_changed or embedding_changed):
                                    skipped_count += 1
                                    continue

                        # Update the document
                        embedding_vector = None
                        if doc.embedding:
                            embedding_vector = np.array(doc.embedding, dtype=np.float32)

                        await conn.execute(
                            """
                            UPDATE embeddings.documents
                            SET content = $2, metadata = $3, embedding = $4,
                                collection_name = $5, updated_at = NOW()
                            WHERE id = $1
                        """,
                            doc.id,
                            doc.content,
                            doc.metadata,
                            embedding_vector,
                            doc.collection_name,
                        )

                        updated_count += 1

                return updated_count, skipped_count

            except Exception as e:
                logger.error(f"Failed to update embeddings batch: {e}")
                raise

    async def delete_documents(self, document_ids: List[str]) -> int:
        """
        Delete multiple documents by their IDs.

        Args:
            document_ids: List of document IDs to delete

        Returns:
            Number of documents deleted
        """
        if not document_ids:
            return 0

        async with self.pool.acquire() as conn:
            try:
                result = await conn.execute(
                    """
                    DELETE FROM embeddings.documents
                    WHERE id = ANY($1)
                """,
                    document_ids,
                )

                deleted_count = int(result.split()[-1])
                logger.info(f"Deleted {deleted_count} documents")
                return deleted_count

            except Exception as e:
                logger.error(f"Failed to delete documents: {e}")
                raise

    async def get_collection_stats(self, collection_name: str) -> Dict[str, Any]:
        """
        Get statistics for a collection.

        Args:
            collection_name: Collection name

        Returns:
            Dictionary with collection statistics
        """
        async with self.pool.acquire() as conn:
            try:
                result = await conn.fetchrow(
                    """
                    SELECT
                        COUNT(*) as document_count,
                        COUNT(CASE WHEN embedding IS NOT NULL THEN 1 END) as embedded_count,
                        MIN(created_at) as first_document,
                        MAX(created_at) as last_document,
                        MAX(updated_at) as last_updated
                    FROM embeddings.documents
                    WHERE collection_name = $1
                """,
                    collection_name,
                )

                if result:
                    return {
                        "collection_name": collection_name,
                        "document_count": result["document_count"],
                        "embedded_count": result["embedded_count"],
                        "first_document": result["first_document"],
                        "last_document": result["last_document"],
                        "last_updated": result["last_updated"],
                    }
                else:
                    return {
                        "collection_name": collection_name,
                        "document_count": 0,
                        "embedded_count": 0,
                        "first_document": None,
                        "last_document": None,
                        "last_updated": None,
                    }

            except Exception as e:
                logger.error(
                    f"Failed to get collection stats for '{collection_name}': {e}"
                )
                raise

    async def list_documents(
        self,
        collection_name: str = "default",
        limit: int = 100,
        offset: int = 0,
        include_embeddings: bool = False,
    ) -> List[EmbeddingDocument]:
        """
        List documents in a collection with pagination.

        Args:
            collection_name: Collection name
            limit: Maximum number of documents to return
            offset: Number of documents to skip
            include_embeddings: Whether to include embedding vectors

        Returns:
            List of documents
        """
        async with self.pool.acquire() as conn:
            try:
                if include_embeddings:
                    select_fields = "id, content, metadata, embedding, collection_name, created_at, updated_at"
                else:
                    select_fields = (
                        "id, content, metadata, collection_name, created_at, updated_at"
                    )

                results = await conn.fetch(
                    f"""
                    SELECT {select_fields}
                    FROM embeddings.documents
                    WHERE collection_name = $1
                    ORDER BY created_at DESC
                    LIMIT $2 OFFSET $3
                """,
                    collection_name,
                    limit,
                    offset,
                )

                documents = []
                for row in results:
                    embedding = None
                    if include_embeddings and "embedding" in row and row["embedding"]:
                        embedding = row["embedding"].tolist()

                    documents.append(
                        EmbeddingDocument(
                            id=row["id"],
                            content=row["content"],
                            metadata=row["metadata"] or {},
                            embedding=embedding,
                            collection_name=row["collection_name"],
                            created_at=row["created_at"],
                            updated_at=row["updated_at"],
                        )
                    )

                return documents

            except Exception as e:
                logger.error(
                    f"Failed to list documents in collection '{collection_name}': {e}"
                )
                raise
